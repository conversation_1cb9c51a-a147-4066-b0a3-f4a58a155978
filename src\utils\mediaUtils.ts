// 媒体文件检测和处理工具函数


/**
 * 从URL提取文件扩展名
 */
export function extractExtensionFromUrl(url: string): string {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const lastDotIndex = pathname.lastIndexOf('.')
    
    if (lastDotIndex === -1) return ''
    
    const extension = pathname.substring(lastDotIndex + 1).toLowerCase()
    
    // 只返回常见的文件扩展名（最多4个字符）
    if (extension.length <= 4) {
      return extension
    }
    
    return ''
  } catch {
    return ''
  }
}

/**
 * 从响应头中提取文件大小
 */
export function extractSizeFromHeaders(headers?: chrome.webRequest.HttpHeader[]): number | undefined {
  if (!headers) return undefined

  // 首先尝试从 Content-Range 头中提取完整文件大小
  const contentRangeHeader = headers.find(
    header => header.name.toLowerCase() === 'content-range'
  )

  if (contentRangeHeader && contentRangeHeader.value) {
    // Content-Range 格式: "bytes start-end/total" 或 "bytes start-end/*"
    // 例如: "bytes 2520845-2620174/40612254"
    const rangeMatch = contentRangeHeader.value.match(/bytes\s+\d+-\d+\/(\d+)/)
    if (rangeMatch && rangeMatch[1]) {
      const totalSize = parseInt(rangeMatch[1], 10)
      if (!isNaN(totalSize)) {
        return totalSize
      }
    }
  }

  // 如果没有 Content-Range 或解析失败，则尝试 Content-Length
  const contentLengthHeader = headers.find(
    header => header.name.toLowerCase() === 'content-length'
  )

  if (contentLengthHeader && contentLengthHeader.value) {
    const size = parseInt(contentLengthHeader.value, 10)
    return isNaN(size) ? undefined : size
  }

  return undefined
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
