// 视图模式存储工具函数
// 用于管理popup和sidepanel之间的切换状态

import type { ToolbarVariant } from "../types/components"

const VIEW_MODE_KEY = 'snapany_view_mode'

/**
 * 获取当前视图模式
 */
export async function getCurrentViewMode(): Promise<ToolbarVariant> {
    try {
        const result = await chrome.storage.local.get([VIEW_MODE_KEY])
        return result[VIEW_MODE_KEY] || 'popup'
    } catch (error) {
        console.error('获取视图模式失败:', error)
        return 'popup'
    }
}

/**
 * 设置当前视图模式
 */
export async function setCurrentViewMode(mode: ToolbarVariant): Promise<boolean> {
    try {
        await chrome.storage.local.set({ [VIEW_MODE_KEY]: mode })
        console.log('视图模式已设置为:', mode)
        return true
    } catch (error) {
        console.error('设置视图模式失败:', error)
        return false
    }
}

/**
 * 切换视图模式
 */
export async function toggleViewMode(): Promise<ToolbarVariant> {
    const currentMode = await getCurrentViewMode()
    const newMode: ToolbarVariant = currentMode === 'popup' ? 'sidepanel' : 'popup'
    await setCurrentViewMode(newMode)
    return newMode
}

/**
 * 监听视图模式变化
 */
export function onViewModeChanged(callback: (mode: ToolbarVariant) => void): () => void {
    const listener = (changes: { [key: string]: chrome.storage.StorageChange }) => {
        if (changes[VIEW_MODE_KEY]) {
            callback(changes[VIEW_MODE_KEY].newValue)
        }
    }

    chrome.storage.onChanged.addListener(listener)

    // 返回清理函数
    return () => {
        chrome.storage.onChanged.removeListener(listener)
    }
}