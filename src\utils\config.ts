// 配置工具函数
// 统一管理应用配置，支持开发和生产环境

/**
 * 环境类型枚举
 */
export enum Environment {
  DEVELOPMENT = 'development',
  PRODUCTION = 'production'
}

/**
 * 获取完整的下载器URL
 * @param requestId 请求ID
 * @returns 完整的下载器URL
 */
export function getDownloaderUrl(requestId: string): string {
  if (isDevelopment()) {
    return `http://localhost:3000/downloader?requestId=${requestId}`
  } else {
    return `https://snapany.com/downloader?requestId=${requestId}`
  }
}

/**
 * 检查当前是否为开发环境
 * @returns 是否为开发环境
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development'
}

/**
 * 检查当前是否为生产环境
 * @returns 是否为生产环境
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production'
}
