// 连接管理模块
// 处理popup和sidepanel的连接管理、消息广播等功能

import { RequestMonitor } from "@/background/services/monitor"
import { ConnectionMessageType } from "@/types/network"

export class ConnectionManager {
  private requestMonitor: RequestMonitor
  private popupPort: chrome.runtime.Port | null = null
  private activePorts = new Set<chrome.runtime.Port>()

  constructor(requestMonitor: RequestMonitor) {
    this.requestMonitor = requestMonitor
    this.setupConnectionListeners()
  }

  // 设置连接监听器
  private setupConnectionListeners() {
    // 监听popup连接
    chrome.runtime.onConnect.addListener((port) => {
      console.log(`${port.name}已连接`)

      // 添加到活动连接集合
      this.activePorts.add(port)

      if (port.name === "popup") {
        this.popupPort = port

        // 发送初始数据
        port.postMessage({
          type: "INITIAL_DATA",
          data: {
            stats: this.requestMonitor.getStats(),
            isMonitoringEnabled: this.requestMonitor.isEnabled()
          }
        })
      } else if (port.name === "sidepanel") {
        // 为sidepanel发送初始数据
        port.postMessage({
          type: "INITIAL_DATA",
          data: {
            stats: this.requestMonitor.getStats(),
            isMonitoringEnabled: this.requestMonitor.isEnabled()
          }
        })
      }

      port.onDisconnect.addListener(() => {
        console.log(`${port.name}已断开连接`)

        // 从活动连接中移除
        this.activePorts.delete(port)

        if (port === this.popupPort) {
          this.popupPort = null
        }
      })
    })
  }

  // 广播更新给所有活动的客户端
  notifyAllClients() {
    const updateData = {
      type: ConnectionMessageType.DATA_UPDATE,
      data: {
        stats: this.requestMonitor.getStats(),
        isMonitoringEnabled: this.requestMonitor.isEnabled()
      }
    }

    // 发送给popup
    if (this.popupPort) {
      try {
        this.popupPort.postMessage(updateData)
      } catch (error) {
        console.error("发送popup更新消息失败:", error)
        this.popupPort = null
      }
    }

    // 发送给所有活动的连接
    this.activePorts.forEach(port => {
      try {
        port.postMessage(updateData)
      } catch (error) {
        console.error("发送更新消息失败:", error)
        this.activePorts.delete(port)
      }
    })

    // 也通过runtime.sendMessage广播（用于sidepanel等非持久连接）
    try {
      chrome.runtime.sendMessage(updateData).catch(() => {
        // 忽略错误，因为可能没有监听器
      })
    } catch (error) {
      // 忽略错误
    }
  }
}
