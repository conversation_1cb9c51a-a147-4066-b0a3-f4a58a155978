// Chrome 扩展后台脚本
// 处理扩展的后台逻辑和消息传递

import { RequestMonitor } from "./services/monitor"
import { MessageHandler } from "./handlers/message"
import { DownloadManager } from "./services/download"
import { HeaderManager } from "./services/header"
import { ConnectionManager } from "./handlers/connection"
import { SidepanelManager } from "./services/sidepanel"
import { CorsManager } from "./services/cors"
import type { Message } from "../types/network"

console.log("后台脚本已启动")

// 创建核心模块实例
const requestMonitor = new RequestMonitor()
const headerManager = new HeaderManager()
const corsManager = new CorsManager()
const downloadManager = new DownloadManager(headerManager)
const connectionManager = new ConnectionManager(requestMonitor)
const sidepanelManager = new SidepanelManager()
const messageHandler = new MessageHandler(requestMonitor)

// 设置模块间的依赖关系
messageHandler.setDependencies({
  downloadManager,
  headerManager,
  sidepanelManager
})

// 设置请求监控更新回调
requestMonitor.setUpdateCallback(() => {
  connectionManager.notifyAllClients()
})

// 初始化扩展
async function initializeExtension() {
  try {
    // 初始化侧边栏行为
    await sidepanelManager.initializeSidePanelBehavior()

    // 启用CORS绕过功能
    await corsManager.enable()

    console.log("扩展初始化完成")
  } catch (error) {
    console.error("扩展初始化失败:", error)
  }
}

// 在扩展启动时初始化
initializeExtension()

// 监听来自内容脚本和popup的消息
chrome.runtime.onMessage.addListener((message: Message, sender, sendResponse) => {
  // 使用消息处理器处理消息
  messageHandler.handleMessage(message, sender, sendResponse)
  return true // 保持消息通道开放以支持异步响应
})

// 监听标签页关闭事件，清理下载任务
chrome.tabs.onRemoved.addListener((tabId: number) => {
  console.log(`标签页 ${tabId} 已关闭，清理相关下载任务`)
  downloadManager.cleanupTabTasks(tabId)
})

// 监听标签页更新事件，处理页面刷新
chrome.tabs.onUpdated.addListener((tabId: number, changeInfo, tab) => {
  // 当页面开始加载新内容时，清理旧的下载任务
  if (changeInfo.status === 'loading' && changeInfo.url) {
    console.log(`标签页 ${tabId} 开始加载新页面，清理旧的下载任务`)
    downloadManager.cleanupTabTasks(tabId)
  }
})


