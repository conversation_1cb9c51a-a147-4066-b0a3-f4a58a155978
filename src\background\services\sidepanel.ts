// 侧边栏管理模块
// 处理侧边栏的初始化、切换和状态管理

import type { Response } from "../../types/network"

export class SidepanelManager {
  
  // 初始化侧边栏行为
  async initializeSidePanelBehavior() {
    try {
      // 获取保存的视图模式
      const result = await chrome.storage.local.get(['snapany_view_mode'])
      const viewMode = result.snapany_view_mode || 'popup'

      if (viewMode === 'sidepanel') {
        // 如果用户上次使用的是侧边栏模式，设置为点击图标打开侧边栏
        await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true })
        console.log("已设置为侧边栏模式")
      } else {
        // 如果是popup模式，禁用侧边栏的自动打开
        await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: false })
        console.log("已设置为弹窗模式")
      }
    } catch (error) {
      console.error("初始化侧边栏行为失败:", error)
    }
  }

  // 处理切换侧边栏显示状态
  async handleToggleSidepanel(sendResponse: (response: Response) => void) {
    try {
      // 设置侧边栏行为，允许通过点击扩展图标打开
      await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true })

      // 设置全局侧边栏选项
      await chrome.sidePanel.setOptions({
        path: 'sidepanel.html',
        enabled: true
      })

      console.log("侧边栏已准备就绪")

      sendResponse({
        success: true,
        message: "侧边栏已准备就绪"
      })
    } catch (error) {
      console.error("设置侧边栏失败:", error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : "设置侧边栏失败"
      })
    }
  }

  // 切换到侧边栏模式
  async switchToSidepanel(): Promise<boolean> {
    try {
      // 保存视图模式设置
      await chrome.storage.local.set({ snapany_view_mode: 'sidepanel' })
      
      // 设置侧边栏行为
      await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true })
      
      // 设置侧边栏选项
      await chrome.sidePanel.setOptions({
        path: 'sidepanel.html',
        enabled: true
      })

      console.log("已切换到侧边栏模式")
      return true
    } catch (error) {
      console.error("切换到侧边栏模式失败:", error)
      return false
    }
  }

  // 切换到弹窗模式
  async switchToPopup(): Promise<boolean> {
    try {
      // 保存视图模式设置
      await chrome.storage.local.set({ snapany_view_mode: 'popup' })
      
      // 禁用侧边栏的自动打开
      await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: false })

      console.log("已切换到弹窗模式")
      return true
    } catch (error) {
      console.error("切换到弹窗模式失败:", error)
      return false
    }
  }

  // 获取当前视图模式
  async getCurrentViewMode(): Promise<'popup' | 'sidepanel'> {
    try {
      const result = await chrome.storage.local.get(['snapany_view_mode'])
      return result.snapany_view_mode || 'popup'
    } catch (error) {
      console.error("获取视图模式失败:", error)
      return 'popup'
    }
  }

  // 打开侧边栏（在指定标签页）
  async openSidepanel(tabId?: number): Promise<boolean> {
    try {
      if (tabId) {
        // 为特定标签页打开侧边栏
        await chrome.sidePanel.open({ tabId })
      } else {
        // 为当前活动标签页打开侧边栏
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
        if (tabs.length > 0 && tabs[0].id) {
          await chrome.sidePanel.open({ tabId: tabs[0].id })
        }
      }
      console.log("侧边栏已打开")
      return true
    } catch (error) {
      console.error("打开侧边栏失败:", error)
      return false
    }
  }

  // 关闭侧边栏（通过切换到popup模式）
  async closeSidepanel(): Promise<boolean> {
    try {
      // 禁用侧边栏的自动打开
      await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: false })
      console.log("侧边栏已关闭")
      return true
    } catch (error) {
      console.error("关闭侧边栏失败:", error)
      return false
    }
  }

  // 检查侧边栏是否可用
  async isSidepanelAvailable(): Promise<boolean> {
    try {
      // 检查Chrome版本是否支持侧边栏
      if (!chrome.sidePanel) {
        return false
      }

      // 尝试获取侧边栏选项来验证可用性
      await chrome.sidePanel.getOptions({})
      return true
    } catch (error) {
      console.error("检查侧边栏可用性失败:", error)
      return false
    }
  }

  // 设置侧边栏选项
  async setSidepanelOptions(options: {
    path?: string
    enabled?: boolean
    tabId?: number
  }): Promise<boolean> {
    try {
      const sidepanelOptions: chrome.sidePanel.PanelOptions = {}
      
      if (options.path) {
        sidepanelOptions.path = options.path
      }
      
      if (typeof options.enabled === 'boolean') {
        sidepanelOptions.enabled = options.enabled
      }

      if (options.tabId) {
        sidepanelOptions.tabId = options.tabId
      }

      await chrome.sidePanel.setOptions(sidepanelOptions)
      console.log("侧边栏选项已设置:", sidepanelOptions)
      return true
    } catch (error) {
      console.error("设置侧边栏选项失败:", error)
      return false
    }
  }

  // 获取侧边栏选项
  async getSidepanelOptions(tabId?: number): Promise<chrome.sidePanel.PanelOptions | null> {
    try {
      const options = tabId 
        ? await chrome.sidePanel.getOptions({ tabId })
        : await chrome.sidePanel.getOptions({})
      return options
    } catch (error) {
      console.error("获取侧边栏选项失败:", error)
      return null
    }
  }

  // 重置侧边栏设置
  async resetSidepanelSettings(): Promise<boolean> {
    try {
      // 重置为默认的popup模式
      await this.switchToPopup()
      
      // 清除存储的视图模式设置
      await chrome.storage.local.remove(['snapany_view_mode'])
      
      console.log("侧边栏设置已重置")
      return true
    } catch (error) {
      console.error("重置侧边栏设置失败:", error)
      return false
    }
  }
}
