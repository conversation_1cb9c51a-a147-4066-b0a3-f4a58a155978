import { useState, useRef, useEffect } from "react"
import type { ReactNode } from "react"

interface CustomScrollAreaProps {
  children: ReactNode
  className?: string
}

export default function CustomScrollArea({ children, className = "" }: CustomScrollAreaProps) {
  const [isScrolling, setIsScrolling] = useState(false)
  const [showScrollbar, setShowScrollbar] = useState(false)
  const [scrollbarHeight, setScrollbarHeight] = useState(0)
  const [scrollbarTop, setScrollbarTop] = useState(0)
  const [isDragging, setIsDragging] = useState(false)

  const containerRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const scrollTimeoutRef = useRef<NodeJS.Timeout>()

  // 更新滚动条状态
  const updateScrollbar = () => {
    if (!containerRef.current || !contentRef.current) return

    const container = containerRef.current
    const content = contentRef.current

    const containerHeight = container.clientHeight
    const contentHeight = content.scrollHeight
    const scrollTop = content.scrollTop

    // 计算是否需要显示滚动条
    const needsScrollbar = contentHeight > containerHeight
    setShowScrollbar(needsScrollbar)

    if (needsScrollbar) {
      // 计算滚动条高度和位置
      const scrollbarHeightRatio = containerHeight / contentHeight
      const newScrollbarHeight = Math.max(20, containerHeight * scrollbarHeightRatio)

      const scrollableHeight = containerHeight - newScrollbarHeight
      const scrollProgress = scrollTop / (contentHeight - containerHeight)
      const newScrollbarTop = scrollProgress * scrollableHeight

      setScrollbarHeight(newScrollbarHeight)
      setScrollbarTop(newScrollbarTop)
    }
  }

  // 处理滚动事件
  const handleScroll = () => {
    setIsScrolling(true)
    updateScrollbar()

    // 清除之前的超时
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    // 设置新的超时来隐藏滚动条
    scrollTimeoutRef.current = setTimeout(() => {
      if (!isDragging) {
        setIsScrolling(false)
      }
    }, 1000) // 1秒后隐藏
  }

  // 处理滚动条拖拽
  const handleScrollbarMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
    setIsScrolling(true)

    const startY = e.clientY
    const startScrollTop = scrollbarTop

    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current || !contentRef.current) return

      const deltaY = e.clientY - startY
      const container = containerRef.current
      const content = contentRef.current

      const containerHeight = container.clientHeight
      const contentHeight = content.scrollHeight
      const scrollableHeight = containerHeight - scrollbarHeight

      const newScrollbarTop = Math.max(0, Math.min(scrollableHeight, startScrollTop + deltaY))
      const scrollProgress = newScrollbarTop / scrollableHeight
      const newScrollTop = scrollProgress * (contentHeight - containerHeight)

      content.scrollTop = newScrollTop
      updateScrollbar()
    }

    const handleMouseUp = () => {
      setIsDragging(false)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)

      // 延迟隐藏滚动条
      setTimeout(() => {
        setIsScrolling(false)
      }, 1000)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  // 处理鼠标进入容器
  const handleMouseEnter = () => {
    if (showScrollbar) {
      setIsScrolling(true)
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }

  // 处理鼠标离开容器
  const handleMouseLeave = () => {
    if (!isDragging) {
      scrollTimeoutRef.current = setTimeout(() => {
        setIsScrolling(false)
      }, 500)
    }
  }

  useEffect(() => {
    updateScrollbar()

    const resizeObserver = new ResizeObserver(() => {
      updateScrollbar()
    })

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }

    return () => {
      resizeObserver.disconnect()
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [children])

  return (
    <div
      ref={containerRef}
      className={`relative ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 内容区域 - 隐藏原生滚动条 */}
      <div
        ref={contentRef}
        className="h-full overflow-y-scroll overflow-x-hidden scrollbar-hide"
        onScroll={handleScroll}
        style={{
          marginRight: '-17px', // 隐藏原生滚动条
          paddingRight: '17px',
          paddingBottom: '8px' // 确保底部内容可见
        }}
      >
        {children}
      </div>

      {/* 自定义滚动条 */}
      {showScrollbar && (
        <div
          className={`absolute right-0 top-0 w-1.5 transition-opacity duration-300 ${isScrolling ? 'opacity-100' : 'opacity-0'
            }`}
        >
          <div
            className="bg-gray-400 hover:bg-gray-500 rounded-full cursor-pointer transition-colors duration-200"
            style={{
              height: `${scrollbarHeight}px`,
              transform: `translateY(${scrollbarTop}px)`
            }}
            onMouseDown={handleScrollbarMouseDown}
          />
        </div>
      )}
    </div>
  )
} 