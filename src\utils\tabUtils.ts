// 标签页和URL处理工具类
// 提供标签页信息获取和URL处理的通用方法

/**
 * 从URL提取域名
 * @param url 要处理的URL
 * @returns 域名字符串，如果解析失败返回 'unknown'
 */
export function extractDomain(url: string): string {
  try {
    return new URL(url).hostname
  } catch {
    return 'unknown'
  }
}

/**
 * 获取标签页标题
 * @param tabId 标签页ID
 * @returns 标签页标题，如果获取失败返回 'Unknown Page'
 */
export async function getPageTitle(tabId: number): Promise<string> {
  try {
    const tab = await chrome.tabs.get(tabId)
    return tab.title || 'Unknown Page'
  } catch {
    return 'Unknown Page'
  }
}

/**
 * 获取标签页URL
 * @param tabId 标签页ID
 * @returns 标签页URL，如果获取失败返回空字符串
 */
export async function getPageUrl(tabId: number): Promise<string> {
  try {
    const tab = await chrome.tabs.get(tabId)
    return tab.url || ''
  } catch {
    return ''
  }
}

/**
 * 获取标签页图标URL
 * @param tabId 标签页ID
 * @returns 标签页图标URL，如果获取失败返回空字符串
 */
export async function getFavIconUrl(tabId: number): Promise<string> {
  try {
    const tab = await chrome.tabs.get(tabId)
    return tab.favIconUrl || ''
  } catch {
    return ''
  }
}

/**
 * 批量获取标签页信息
 * @param tabId 标签页ID
 * @returns 包含标题、URL和图标的对象
 */
export async function getTabInfo(tabId: number): Promise<{
  title: string
  url: string
  favIconUrl: string
}> {
  try {
    const tab = await chrome.tabs.get(tabId)
    return {
      title: tab.title || 'Unknown Page',
      url: tab.url || '',
      favIconUrl: tab.favIconUrl || ''
    }
  } catch {
    return {
      title: 'Unknown Page',
      url: '',
      favIconUrl: ''
    }
  }
} 